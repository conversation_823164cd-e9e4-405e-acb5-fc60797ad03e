import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ManagersCountBadge } from './ManagersCountBadge';
import { IManagerProfile } from '../../profile-mock-data';

// Mock scrollIntoView which is not available in JSDOM
Object.defineProperty(window.HTMLElement.prototype, 'scrollIntoView', {
  value: jest.fn(),
  writable: true,
});

// Mock data for testing
const mockSingleManager: IManagerProfile[] = [
  {
    id: 1,
    name: '<PERSON>',
    title: 'Manager',
    avatar: 'assets/images/user-1.jpg',
  },
];

const mockMultipleManagers: IManagerProfile[] = [
  {
    id: 1,
    name: '<PERSON>',
    title: 'Manager',
    avatar: 'assets/images/user-1.jpg',
  },
  {
    id: 2,
    name: '<PERSON>',
    title: 'Senior Manager',
    avatar: 'assets/images/user-2.jpg',
  },
  {
    id: 3,
    name: '<PERSON>',
    title: 'Team Lead',
    avatar: 'assets/images/user-3.jpg',
  },
];

const mockEmptyManagers: IManagerProfile[] = [];

describe('ManagersCountBadge', () => {
  describe('Rendering Logic', () => {
    it('should not render when there are no managers', () => {
      render(<ManagersCountBadge managers={mockEmptyManagers} testId="test-badge" />);

      expect(screen.queryByTestId('test-badge')).not.toBeInTheDocument();
    });

    it('should not render when there is only one manager', () => {
      render(<ManagersCountBadge managers={mockSingleManager} testId="test-badge" />);

      expect(screen.queryByTestId('test-badge')).not.toBeInTheDocument();
    });

    it('should render when there are multiple managers', () => {
      render(<ManagersCountBadge managers={mockMultipleManagers} testId="test-badge" />);

      expect(screen.getByTestId('test-badge')).toBeInTheDocument();
    });

    it('should display the correct count', () => {
      render(<ManagersCountBadge managers={mockMultipleManagers} testId="test-badge" />);

      expect(screen.getByText('3')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA label for the popover button', () => {
      render(<ManagersCountBadge managers={mockMultipleManagers} testId="test-badge" />);

      const popoverButton = screen.getByRole('button');
      expect(popoverButton).toHaveAttribute('aria-label', 'View all 3 managers');
    });

    it('should have proper ARIA label for the count', () => {
      render(<ManagersCountBadge managers={mockMultipleManagers} testId="test-badge" />);

      const countElement = screen.getByLabelText('3 managers total');
      expect(countElement).toBeInTheDocument();
    });

    it('should be keyboard accessible', () => {
      render(<ManagersCountBadge managers={mockMultipleManagers} testId="test-badge" />);

      const popoverButton = screen.getByRole('button');
      expect(popoverButton).toHaveAttribute('tabindex', '0');
    });
  });

  describe('Popover Menu Interactions', () => {
    it('should render popover menu button', () => {
      render(<ManagersCountBadge managers={mockMultipleManagers} testId="test-badge" />);

      const popoverButton = screen.getByRole('button');
      expect(popoverButton).toBeInTheDocument();
    });

    it('should have correct popover menu structure', () => {
      render(<ManagersCountBadge managers={mockMultipleManagers} testId="test-badge" />);

      // Check that the popover menu is rendered with correct test ID
      expect(screen.getByTestId('test-badge-popover')).toBeInTheDocument();
    });

    it('should render correct number of manager items', () => {
      const { container } = render(<ManagersCountBadge managers={mockMultipleManagers} testId="test-badge" />);

      // Check that we have the expected number of PopoverMenuItem components
      // This tests the structure without triggering the problematic scrollIntoView
      const managerItems = container.querySelectorAll('[data-testid*="test-badge-manager-"]');
      expect(managerItems).toHaveLength(3);
    });
  });

  describe('Props Handling', () => {
    it('should use custom testId prop', () => {
      render(<ManagersCountBadge managers={mockMultipleManagers} testId="custom-test-id" />);

      expect(screen.getByTestId('custom-test-id')).toBeInTheDocument();
    });

    it('should use default testId when not provided', () => {
      render(<ManagersCountBadge managers={mockMultipleManagers} />);

      expect(screen.getByTestId('managers-count-badge')).toBeInTheDocument();
    });

    it('should handle empty manager data gracefully', () => {
      const managersWithMissingData = [
        { id: 1, name: '', title: '', avatar: '' },
        { id: 2, name: 'Jane Smith', title: 'Manager', avatar: 'avatar.jpg' },
      ];

      render(<ManagersCountBadge managers={managersWithMissingData} testId="test-badge" />);

      expect(screen.getByTestId('test-badge')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle exactly 2 managers', () => {
      const twoManagers = mockMultipleManagers.slice(0, 2);
      render(<ManagersCountBadge managers={twoManagers} testId="test-badge" />);

      expect(screen.getByTestId('test-badge')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument();
    });

    it('should handle large number of managers', () => {
      const manyManagers = Array.from({ length: 10 }, (_, index) => ({
        id: index + 1,
        name: `Manager ${index + 1}`,
        title: `Title ${index + 1}`,
        avatar: `avatar-${index + 1}.jpg`,
      }));

      render(<ManagersCountBadge managers={manyManagers} testId="test-badge" />);

      expect(screen.getByTestId('test-badge')).toBeInTheDocument();
      expect(screen.getByText('10')).toBeInTheDocument();
    });
  });
});
