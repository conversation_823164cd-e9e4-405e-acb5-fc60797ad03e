import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ProfileManager } from './ProfileManager';
import { mockBio } from '../../profile-mock-data';

// Mock scrollIntoView which is not available in JSDOM
Object.defineProperty(window.HTMLElement.prototype, 'scrollIntoView', {
  value: jest.fn(),
  writable: true,
});

// Mock the profile mock data
jest.mock('../../profile-mock-data', () => ({
  mockBio: {
    managers: [
      {
        id: 1,
        name: '<PERSON><PERSON>',
        title: 'Head of Customer Service',
        avatar: 'assets/images/user-cage.png',
      },
      {
        id: 2,
        name: '<PERSON>',
        title: 'Regional Sales Manager',
        avatar: 'assets/images/user-taro.jpg',
      },
      {
        id: 3,
        name: '<PERSON>',
        title: 'Regional Sales Manager',
        avatar: 'assets/images/user-cage.png',
      },
    ],
  },
}));

describe('ProfileManager', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering with Multiple Managers', () => {
    it('should render with "Managers" label when there are multiple managers', () => {
      render(<ProfileManager />);

      expect(screen.getByText('Managers')).toBeInTheDocument();
    });

    it('should display the primary manager information', () => {
      render(<ProfileManager />);

      expect(screen.getByText('Macie Burke')).toBeInTheDocument();
      expect(screen.getByText('Head of Customer Service')).toBeInTheDocument();
    });

    it('should render the PopoverMenu button when there are multiple managers', () => {
      render(<ProfileManager />);

      expect(screen.getByTestId('managers-popover-popover-menu-button')).toBeInTheDocument();
      expect(screen.getByText('+2')).toBeInTheDocument(); // Shows +2 for 3 total managers (1 primary + 2 others)
    });

    it('should render manager avatar with correct props', () => {
      render(<ProfileManager />);

      const avatar = screen.getByLabelText('Manager Avatar');
      expect(avatar).toBeInTheDocument();
    });
  });

  describe('Rendering with Single Manager', () => {
    beforeEach(() => {
      // Mock single manager scenario
      jest.doMock('../../profile-mock-data', () => ({
        mockBio: {
          managers: [
            {
              id: 1,
              name: 'John Doe',
              title: 'Manager',
              avatar: 'assets/images/user-1.jpg',
            },
          ],
        },
      }));
    });

    it('should render with "Manager" label when there is only one manager', () => {
      // Re-import to get the mocked data
      jest.resetModules();
      const { ProfileManager: SingleManagerComponent } = require('./ProfileManager');

      render(<SingleManagerComponent />);

      expect(screen.getByText('Manager')).toBeInTheDocument();
    });

    it('should not render PopoverMenu button when there is only one manager', () => {
      jest.resetModules();
      const { ProfileManager: SingleManagerComponent } = require('./ProfileManager');

      render(<SingleManagerComponent />);

      expect(screen.queryByTestId('managers-popover-popover-menu-button')).not.toBeInTheDocument();
    });
  });

  describe('Rendering with No Managers', () => {
    beforeEach(() => {
      // Mock no managers scenario
      jest.doMock('../../profile-mock-data', () => ({
        mockBio: {
          managers: [],
        },
      }));
    });

    it('should not render anything when there are no managers', () => {
      jest.resetModules();
      const { ProfileManager: NoManagerComponent } = require('./ProfileManager');

      const { container } = render(<NoManagerComponent />);

      expect(container.firstChild).toBeNull();
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined managers array gracefully', () => {
      jest.doMock('../../profile-mock-data', () => ({
        mockBio: {
          managers: undefined,
        },
      }));

      jest.resetModules();
      const { ProfileManager: UndefinedManagerComponent } = require('./ProfileManager');

      const { container } = render(<UndefinedManagerComponent />);

      expect(container.firstChild).toBeNull();
    });

    it('should handle null managers array gracefully', () => {
      jest.doMock('../../profile-mock-data', () => ({
        mockBio: {
          managers: null,
        },
      }));

      jest.resetModules();
      const { ProfileManager: NullManagerComponent } = require('./ProfileManager');

      const { container } = render(<NullManagerComponent />);

      expect(container.firstChild).toBeNull();
    });
  });

  describe('Component Structure', () => {
    it('should have proper CSS classes for styling', () => {
      render(<ProfileManager />);

      const stackPanelItem = screen.getByText('Managers').closest('.detail-item.manager');
      expect(stackPanelItem).toBeInTheDocument();
    });

    it('should have proper component structure', () => {
      render(<ProfileManager />);

      // Check that the main container has the correct CSS classes
      const stackPanelItem = screen.getByText('Managers').closest('.detail-item.manager');
      expect(stackPanelItem).toBeInTheDocument();

      // Check that the PopoverMenu button is present for multiple managers
      expect(screen.getByTestId('managers-popover-popover-menu-button')).toBeInTheDocument();
    });

    it('should render MediaObject with correct props', () => {
      render(<ProfileManager />);

      // Check that MediaObject is rendered with expected content
      expect(screen.getByText('Macie Burke')).toBeInTheDocument();
      expect(screen.getByText('Head of Customer Service')).toBeInTheDocument();
    });
  });
});
