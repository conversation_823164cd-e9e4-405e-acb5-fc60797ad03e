import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ProfileManager } from './ProfileManager';

// Mock scrollIntoView which is not available in JSDOM
Object.defineProperty(window.HTMLElement.prototype, 'scrollIntoView', {
  value: jest.fn(),
  writable: true,
});

// Mock data for different scenarios
const mockMultipleManagers = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    title: 'Head of Customer Service',
    avatar: 'assets/images/user-cage.png',
  },
  {
    id: 2,
    name: '<PERSON>',
    title: 'Regional Sales Manager',
    avatar: 'assets/images/user-taro.jpg',
  },
  {
    id: 3,
    name: '<PERSON>',
    title: 'Regional Sales Manager',
    avatar: 'assets/images/user-cage.png',
  },
];

const mockSingleManager = [
  {
    id: 1,
    name: '<PERSON>',
    title: 'Senior Director',
    avatar: 'assets/images/user-1.jpg',
  },
];

const mockEmptyManagers: any[] = [];

// Mock the profile mock data module
jest.mock('../../profile-mock-data', () => ({
  mockBio: {
    managers: [], // Default empty, will be overridden in tests
  },
}));

describe('ProfileManager', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering with Multiple Managers', () => {
    it('should render with "Managers" label when there are multiple managers', () => {
      render(<ProfileManager managers={mockMultipleManagers} />);

      expect(screen.getByText('Managers')).toBeInTheDocument();
    });

    it('should display the primary manager information', () => {
      render(<ProfileManager managers={mockMultipleManagers} />);

      expect(screen.getByText('Macie Burke')).toBeInTheDocument();
      expect(screen.getByText('Head of Customer Service')).toBeInTheDocument();
    });

    it('should render the PopoverMenu button when there are multiple managers', () => {
      render(<ProfileManager managers={mockMultipleManagers} />);

      expect(screen.getByTestId('managers-popover-popover-menu-button')).toBeInTheDocument();
      expect(screen.getByText('+2')).toBeInTheDocument(); // Shows +2 for 3 total managers (1 primary + 2 others)
    });

    it('should render manager avatar with correct props', () => {
      render(<ProfileManager managers={mockMultipleManagers} />);

      const avatar = screen.getByLabelText('Manager Avatar');
      expect(avatar).toBeInTheDocument();
    });
  });

  describe('Rendering with Single Manager', () => {
    it('should render with "Manager" label when there is only one manager', () => {
      render(<ProfileManager managers={mockSingleManager} />);

      // Look specifically for the h5 element with "Manager" text
      expect(screen.getByRole('heading', { level: 5, name: 'Manager' })).toBeInTheDocument();
    });

    it('should not render PopoverMenu button when there is only one manager', () => {
      render(<ProfileManager managers={mockSingleManager} />);

      expect(screen.queryByTestId('managers-popover-popover-menu-button')).not.toBeInTheDocument();
    });

    it('should display the single manager information', () => {
      render(<ProfileManager managers={mockSingleManager} />);

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Senior Director')).toBeInTheDocument();
    });

    it('should render manager avatar with correct props', () => {
      render(<ProfileManager managers={mockSingleManager} />);

      const avatar = screen.getByLabelText('Manager Avatar');
      expect(avatar).toBeInTheDocument();
    });
  });

  describe('Rendering with No Managers', () => {
    it('should not render anything when there are no managers', () => {
      const { container } = render(<ProfileManager managers={mockEmptyManagers} />);

      expect(container.firstChild).toBeNull();
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined managers array gracefully', () => {
      const { container } = render(<ProfileManager managers={undefined} />);

      expect(container.firstChild).toBeNull();
    });

    it('should handle null managers array gracefully', () => {
      const { container } = render(<ProfileManager managers={null as any} />);

      expect(container.firstChild).toBeNull();
    });

    it('should use mock data when no managers prop is provided', () => {
      // This test verifies the component still works with the original mock data approach
      render(<ProfileManager />);

      // Should render something (either from mock data or handle gracefully)
      const component = screen.queryByTestId('profile-manager');
      // Component should either render with mock data or be null, both are acceptable
      expect(component).toBeDefined();
    });
  });

  describe('Component Structure', () => {
    it('should have proper CSS classes for styling', () => {
      render(<ProfileManager managers={mockMultipleManagers} />);

      const stackPanelItem = screen.getByText('Managers').closest('.detail-item.manager');
      expect(stackPanelItem).toBeInTheDocument();
    });

    it('should have proper component structure', () => {
      render(<ProfileManager managers={mockMultipleManagers} />);

      // Check that the main container has the correct CSS classes
      const stackPanelItem = screen.getByText('Managers').closest('.detail-item.manager');
      expect(stackPanelItem).toBeInTheDocument();

      // Check that the PopoverMenu button is present for multiple managers
      expect(screen.getByTestId('managers-popover-popover-menu-button')).toBeInTheDocument();
    });

    it('should render MediaObject with correct props', () => {
      render(<ProfileManager managers={mockMultipleManagers} />);

      // Check that MediaObject is rendered with expected content
      expect(screen.getByText('Macie Burke')).toBeInTheDocument();
      expect(screen.getByText('Head of Customer Service')).toBeInTheDocument();
    });

    it('should render with testId prop', () => {
      render(<ProfileManager managers={mockSingleManager} testId="custom-test-id" />);

      expect(screen.getByTestId('custom-test-id')).toBeInTheDocument();
    });
  });
});
