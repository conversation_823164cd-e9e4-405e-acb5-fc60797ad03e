import React from 'react';
import { StackPanelItem } from '@ceridianhcm/everest-cdk';
import { MediaObject, Avatar } from '@ceridianhcm/components';
import { mockBio } from '../../profile-mock-data';
import { ManagersCountBadge } from '../ManagersCountBadge';
import './profile-manager.scss';

/**
 * ProfileManager displays the primary manager information and a count badge
 * when there are multiple managers. The count badge opens a popover menu
 * showing all managers when clicked.
 */
export const ProfileManager: React.FC = () => {
  const managers = mockBio.managers || [];
  const primaryManager = managers[0];
  const hasMultipleManagers = managers.length > 1;

  // If no managers, don't render anything
  if (!primaryManager) {
    return null;
  }

  return (
    <StackPanelItem className="detail-item manager">
      <div className="manager-header">
        <h5>{hasMultipleManagers ? 'Managers' : 'Manager'}</h5>
        {hasMultipleManagers && (
          <ManagersCountBadge
            managers={managers}
            testId="profile-managers-count-badge"
            id="profile-managers-count-badge"
          />
        )}
      </div>
      <MediaObject
        media={<Avatar id="manager-avatar" ariaLabel="Manager Avatar" size="md" src={primaryManager.avatar} />}
        id="manager-media-object"
        title={<h4 className="evrHeading4">{primaryManager.name}</h4>}
        subtitle={primaryManager.title}
        gap="--evr-spacing-sm"
        mediaAlignment="center"
      />
    </StackPanelItem>
  );
};

export default ProfileManager;
