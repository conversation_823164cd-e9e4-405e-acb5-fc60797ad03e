import React from 'react';
import { StackPanelItem } from '@ceridianhcm/everest-cdk';
import { MediaObject, Avatar, PopoverMenu, PopoverMenuItem } from '@ceridianhcm/components';
import { mockBio, IManagerProfile } from '../../profile-mock-data';
import './profile-manager.scss';

/**
 * Props for the ProfileManager component
 */
export interface ProfileManagerProps {
  /** Array of managers to display. If not provided, uses mock data */
  managers?: IManagerProfile[];
  /** Test ID for testing purposes */
  testId?: string;
}

/**
 * ProfileManager displays the primary manager information with a clickable count tag
 * when there are multiple managers. The tag opens a popover menu showing all managers.
 */
export const ProfileManager: React.FC<ProfileManagerProps> = ({
  managers: propManagers,
  testId = 'profile-manager',
}) => {
  const managers = propManagers || mockBio.managers || [];
  const primaryManager = managers[0];
  const hasMultipleManagers = managers.length > 1;

  // If no managers, don't render anything
  if (!primaryManager) {
    return null;
  }

  const handleManagerSelect = ({ id: selectedId }: { id: string }) => {
    // Handle manager selection if needed in the future
    console.log('Manager selected:', selectedId);
  };

  // Create the title with the clickable count tag if there are multiple managers
  const managerTitle = hasMultipleManagers ? (
    <>
      <h4 className="evrHeading4">
        {primaryManager.name}
        <PopoverMenu
          id="managers-popover"
          triggerOption="segmentedInformational"
          buttonLabel={`+${managers.length - 1}`}
          buttonAriaLabel={`View all ${managers.length} managers`}
          onChange={handleManagerSelect}
          testId="managers-popover"
        >
          {managers.map((manager) => (
            <PopoverMenuItem key={manager.id} id={`manager-${manager.id}`} testId={`manager-item-${manager.id}`}>
              <MediaObject
                media={
                  <Avatar
                    id={`manager-avatar-${manager.id}`}
                    ariaLabel={`${manager.name} Avatar`}
                    size="md"
                    src={manager.avatar}
                    testId={`manager-avatar-${manager.id}`}
                  />
                }
                id={`manager-media-object-${manager.id}`}
                title={<h4 className="evrHeading4">{manager.name}</h4>}
                subtitle={manager.title}
                gap="--evr-spacing-sm"
                mediaAlignment="center"
              />
            </PopoverMenuItem>
          ))}
        </PopoverMenu>
      </h4>
    </>
  ) : (
    <h4 className="evrHeading4">{primaryManager.name}</h4>
  );

  return (
    <StackPanelItem className="detail-item manager">
      <div data-testid={testId}>
        <h5>{hasMultipleManagers ? 'Managers' : 'Manager'}</h5>
        <MediaObject
          media={<Avatar id="manager-avatar" ariaLabel="Manager Avatar" size="md" src={primaryManager.avatar} />}
          id="manager-media-object"
          title={managerTitle}
          subtitle={primaryManager.title}
          gap="--evr-spacing-sm"
          mediaAlignment="center"
        />
      </div>
    </StackPanelItem>
  );
};

export default ProfileManager;
